/* style.css for static home page */
body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  background: #e3f6fd;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

nav {
  background: #0288d1;
  padding: 1.3em 1.7em 1em 1.7em;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
  opacity: 0.95;
}

.title {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.nav-links {
  display: flex;
  gap: 4rem;
  flex: 1;
  justify-content: center;
}

nav a {
  color: #fff;
  text-decoration: none;
  font-weight: bold;
  transition: color 0.2s;
  position: relative;
  padding-bottom: 3px;
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: white;
  transition: width 0.3s ease;
}

.nav-link:hover {
  color: white;
}

.nav-link:hover::after {
  width: 100%;
}

.hero {
  position: relative;
  background: url("../images/poolUnsplash.jpg") center/cover no-repeat;
  color: #fff;
  padding: 6rem 2rem;
  box-shadow: 0 4px 16px #0288d133;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("../images/poolUnsplash.jpg") center/cover no-repeat;
  filter: blur(8px);
  z-index: 0;
}

.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(2, 136, 209, 0.3);
  z-index: 1;
}

.hero h1,
.hero p {
  position: relative;
  z-index: 2;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 8px #0288d199;
}

.hero p {
  font-size: 1.5rem;
  text-shadow: 1px 1px 6px #0288d188;
}

.content {
  max-width: 900px;
  margin: 2rem auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px #0288d122;
  padding: 2rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.instagram-subtitle {
  text-align: center;
  color: #666;
  font-style: italic;
  margin-bottom: 2rem;
}

.instagram-feed {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
  max-width: 900px;
  margin: 0 auto;
}

.instagram-post {
  position: relative;
  aspect-ratio: 1;
  overflow: hidden;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.instagram-post:hover {
  transform: scale(1.05);
}

.instagram-post img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.post-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.instagram-post:hover .post-overlay {
  opacity: 1;
}

.post-stats {
  display: flex;
  gap: 1rem;
  color: white;
  font-weight: bold;
}

.post-stats span {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

footer {
  background: #0288d1;
  color: #fff;
  padding: 1rem 0;
  margin-top: auto;
  display: flex;
  justify-content: center;
  align-items: center;
}
